// Simple CDP JavaScript implementation
// Converted from TypeScript for Node.js usage

const UNDEFINED_VALUE = undefined;
const MESSAGE_EVENT = "message";
const OPEN_EVENT = "open";
const CLOSE_EVENT = "close";
const ERROR_EVENT = "error";
const CONNECTION_REFUSED_ERROR_CODE = "ConnectionRefused";
const CONNECTION_ERROR_CODE = "ConnectionError";
const MIN_INVALID_HTTP_STATUS_CODE = 400;
const GET_METHOD = "GET";
const PUT_METHOD = "PUT";
const DEFAULT_URL = "http://localhost:9222";
const DEFAULT_PATH = "json/version";
const DEFAULT_PATH_TARGETS = "json";
const DEFAULT_PATH_NEW_TARGET = "json/new";
const DEFAULT_PATH_ACTIVATE_TARGET = "json/activate";
const DEFAULT_PATH_CLOSE_TARGET = "json/close";
const DEFAULT_CONNECTION_MAX_RETRY = 20;
const DEFAULT_CONNECTION_RETRY_DELAY = 500;

const DEFAULT_OPTIONS = {
  apiUrl: DEFAULT_URL,
  apiPath: DEFAULT_PATH,
  apiPathTargets: DEFAULT_PATH_TARGETS,
  apiPathNewTarget: DEFAULT_PATH_NEW_TARGET,
  apiPathActivateTarget: DEFAULT_PATH_ACTIVATE_TARGET,
  apiPathCloseTarget: DEFAULT_PATH_CLOSE_TARGET,
  connectionMaxRetry: DEFAULT_CONNECTION_MAX_RETRY,
  connectionRetryDelay: DEFAULT_CONNECTION_RETRY_DELAY,
};

class CDP extends EventTarget {
  #connection = undefined;
  #options = Object.assign({}, DEFAULT_OPTIONS);
  #pendingEventListenerCalls = new Map();

  constructor(options = {}) {
    super();
    Object.assign(this.#options, options);

    // Initialize domain properties directly on the instance
    const domainNames = [
      "Runtime",
      "Target",
      "Page",
      "Console",
      "Network",
      "Input",
      "DOM",
      "CSS",
      "Debugger",
      "Profiler",
      "HeapProfiler",
      "Security",
      "ServiceWorker",
      "Storage",
      "SystemInfo",
      "Browser",
      "Emulation",
      "Animation",
      "Accessibility",
    ];

    domainNames.forEach((domainName) => {
      this[domainName] = this.createDomain(domainName);
    });

    // Create a proxy to handle any additional domains
    return new Proxy(this, {
      get(target, propertyName) {
        if (typeof propertyName === "string") {
          // If property exists, return it
          if (propertyName in target) {
            return target[propertyName];
          }
          // Otherwise, create a new domain
          target[propertyName] = target.createDomain(propertyName);
          return target[propertyName];
        }
        return target[propertyName];
      },
    });
  }

  createDomain(domainName) {
    const cdp = this;

    const domainProxy = new Proxy(Object.create(null), {
      get(domainTarget, methodName) {
        if (typeof methodName === "string") {
          if (methodName === "addEventListener") {
            return cdp.getDomainListenerFunction(
              "addEventListener",
              domainName
            );
          } else if (methodName === "removeEventListener") {
            return cdp.getDomainListenerFunction(
              "removeEventListener",
              domainName
            );
          } else {
            // Always create and cache the method function
            if (!domainTarget[methodName]) {
              domainTarget[methodName] = cdp.getDomainMethodFunction(
                methodName,
                domainName
              );
            }
            return domainTarget[methodName];
          }
        }
        return undefined;
      },
    });

    return domainProxy;
  }

  getDomainMethodFunction(methodName, domainName) {
    const cdp = this;
    return async function (params = {}, sessionId) {
      await cdp.ready();
      const method = `${domainName}.${methodName}`;
      return cdp.connection.sendMessage(method, params, sessionId);
    };
  }

  getDomainListenerFunction(listenerMethodName, domainName) {
    const cdp = this;
    return function (type, listener) {
      const eventType = `${domainName}.${type}`;
      if (listenerMethodName === "addEventListener") {
        if (cdp.#connection === UNDEFINED_VALUE) {
          // Store for later when connection is established
          if (!cdp.#pendingEventListenerCalls.has(eventType)) {
            cdp.#pendingEventListenerCalls.set(eventType, []);
          }
          cdp.#pendingEventListenerCalls.get(eventType).push({
            methodName: listenerMethodName,
            domainName,
            type,
            listener,
          });
        } else {
          cdp.connection.addEventListener(eventType, listener);
        }
      } else if (listenerMethodName === "removeEventListener") {
        if (cdp.#connection !== UNDEFINED_VALUE) {
          cdp.connection.removeEventListener(eventType, listener);
        }
      }
    };
  }

  async ready() {
    if (this.#connection === UNDEFINED_VALUE) {
      console.log(`[kazeel:simple-cdp] CDP: Establishing connection...`);
      let webSocketDebuggerUrl = this.#options.webSocketDebuggerUrl;
      if (webSocketDebuggerUrl === UNDEFINED_VALUE) {
        console.log(
          `[kazeel:simple-cdp] CDP: No WebSocket URL provided, fetching from API...`
        );
        const url = new URL(this.#options.apiPath, this.#options.apiUrl);
        const result = await fetchData(url, this.#options);
        webSocketDebuggerUrl = result.webSocketDebuggerUrl;
        console.log(
          `[kazeel:simple-cdp] CDP: Retrieved WebSocket URL: ${webSocketDebuggerUrl}`
        );
      }
      const connection = new Connection(webSocketDebuggerUrl);
      await connection.open();
      this.#connection = connection;
      console.log(
        `[kazeel:simple-cdp] CDP: Connection established successfully`
      );

      // Process pending event listener calls
      for (const [eventType, calls] of this.#pendingEventListenerCalls) {
        for (const call of calls) {
          if (call.methodName === "addEventListener") {
            this.#connection.addEventListener(eventType, call.listener);
          }
        }
      }
      this.#pendingEventListenerCalls.clear();
    }
    // Removed the "already established" log to reduce noise
  }

  get options() {
    return this.#options;
  }

  set options(value) {
    Object.assign(this.#options, value);
  }

  get connection() {
    if (!this.#connection) {
      throw new Error(
        "Connection not established. Call a CDP method first to establish connection."
      );
    }
    return this.#connection;
  }

  reset() {
    if (this.#connection) {
      console.log(`[kazeel:simple-cdp] CDP: Resetting connection`);
      this.#connection.close();
      this.#connection = UNDEFINED_VALUE;
    } else {
      console.log(
        `[kazeel:simple-cdp] CDP: Reset called but no connection exists`
      );
    }
  }

  static getTargets() {
    const { apiPathTargets, apiUrl } = options;
    return fetchData(new URL(apiPathTargets, apiUrl), options);
  }

  static createTarget(url) {
    const { apiPathNewTarget, apiUrl } = options;
    const path = url
      ? `${apiPathNewTarget}?${encodeURIComponent(url)}`
      : apiPathNewTarget;
    return fetchData(new URL(path, apiUrl), options, PUT_METHOD);
  }

  static async activateTarget(targetId) {
    const { apiPathActivateTarget, apiUrl } = options;
    await fetchData(
      new URL(`${apiPathActivateTarget}/${targetId}`, apiUrl),
      options,
      GET_METHOD,
      false
    );
  }

  static async closeTarget(targetId) {
    const { apiPathCloseTarget, apiUrl } = options;
    await fetchData(
      new URL(`${apiPathCloseTarget}/${targetId}`, apiUrl),
      options,
      GET_METHOD,
      false
    );
  }
}

const options = Object.assign({}, DEFAULT_OPTIONS);
const cdp = new CDP(options);
const getTargets = CDP.getTargets;
const createTarget = CDP.createTarget;
const activateTarget = CDP.activateTarget;
const closeTarget = CDP.closeTarget;

class Connection extends EventTarget {
  #webSocket = undefined;
  #webSocketDebuggerUrl;
  #pendingRequests = new Map();
  #nextRequestId = 1;
  #keepAliveInterval = undefined;

  constructor(webSocketDebuggerUrl) {
    super();
    this.#webSocketDebuggerUrl = webSocketDebuggerUrl;
  }

  open() {
    console.log(
      `[kazeel:simple-cdp] CDP WebSocket: Attempting to connect to ${
        this.#webSocketDebuggerUrl
      }`
    );
    this.#webSocket = new WebSocket(this.#webSocketDebuggerUrl);

    this.#webSocket.addEventListener(MESSAGE_EVENT, (event) => {
      this.#onMessage(JSON.parse(event.data));
    });

    return new Promise((resolve, reject) => {
      this.#webSocket.addEventListener(OPEN_EVENT, () => {
        console.log(
          `[kazeel:simple-cdp] CDP WebSocket: Connection opened successfully`
        );
        this.#startKeepAlive();
        resolve();
      });

      this.#webSocket.addEventListener(CLOSE_EVENT, (event) => {
        console.log(
          `[kazeel:simple-cdp] CDP WebSocket: Connection closed - Code: ${event.code}, Reason: ${event.reason}, WasClean: ${event.wasClean}`
        );
        this.#stopKeepAlive();
        reject(new Error(event.reason));
      });

      this.#webSocket.addEventListener(ERROR_EVENT, (event) => {
        console.log(`[kazeel:simple-cdp] CDP WebSocket: Error occurred`, event);
        reject(new Error("WebSocket error"));
      });
    });
  }

  sendMessage(method, params = {}, sessionId) {
    if (!this.#webSocket) {
      throw new Error("WebSocket not connected");
    }

    const id = this.#nextRequestId;
    const message = JSON.stringify({ id, method, params, sessionId });
    this.#nextRequestId = (this.#nextRequestId + 1) % Number.MAX_SAFE_INTEGER;
    this.#webSocket.send(message);

    let pendingRequest;
    const promise = new Promise(
      (resolve, reject) =>
        (pendingRequest = { resolve, reject, method, params, sessionId })
    );
    this.#pendingRequests.set(id, pendingRequest);

    return promise;
  }

  close() {
    this.#stopKeepAlive();
    if (this.#webSocket) {
      this.#webSocket.close();
    }
  }

  #startKeepAlive() {
    console.log(`[kazeel:simple-cdp] CDP WebSocket: Starting keep-alive`);
    this.#keepAliveInterval = setInterval(() => {
      if (this.#webSocket && this.#webSocket.readyState === WebSocket.OPEN) {
        this.#webSocket.ping();
      }
    }, 30000);
  }

  #stopKeepAlive() {
    if (this.#keepAliveInterval) {
      console.log(`[kazeel:simple-cdp] CDP WebSocket: Stopping keep-alive`);
      clearInterval(this.#keepAliveInterval);
      this.#keepAliveInterval = undefined;
    }
  }

  #onMessage({ id, method, result, error, params, sessionId }) {
    if (id !== UNDEFINED_VALUE) {
      const pendingRequest = this.#pendingRequests.get(id);
      if (pendingRequest) {
        const { resolve, reject } = pendingRequest;
        if (error === UNDEFINED_VALUE) {
          resolve(result);
        } else {
          const cdpError = new Error(error.message);
          cdpError.code = error.code;
          reject(cdpError);
        }
        this.#pendingRequests.delete(id);
      }
    }
    if (method !== UNDEFINED_VALUE) {
      const event = new Event(method);
      event.params = params;
      event.sessionId = sessionId;
      this.dispatchEvent(event);
    }
  }
}

function fetchData(url, options, method = GET_METHOD, parseJSON = true) {
  return retryConnection(async () => {
    let response;
    try {
      response = await fetch(url, { method });
    } catch (error) {
      const cdpError = error;
      cdpError.code = CONNECTION_REFUSED_ERROR_CODE;
      throw cdpError;
    }
    if (response.status >= MIN_INVALID_HTTP_STATUS_CODE) {
      const cdpError = new Error(
        `HTTP ${response.status}: ${response.statusText}`
      );
      cdpError.code = CONNECTION_ERROR_CODE;
      throw cdpError;
    }
    return parseJSON ? response.json() : response.text();
  }, options);
}

async function retryConnection(operation, options) {
  let lastError;
  for (let attempt = 0; attempt <= options.connectionMaxRetry; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      if (
        attempt < options.connectionMaxRetry &&
        (error.code === CONNECTION_REFUSED_ERROR_CODE ||
          error.code === CONNECTION_ERROR_CODE)
      ) {
        console.log(
          `[kazeel:simple-cdp] Connection attempt ${
            attempt + 1
          } failed, retrying in ${options.connectionRetryDelay}ms...`
        );
        await new Promise((resolve) =>
          setTimeout(resolve, options.connectionRetryDelay)
        );
      } else {
        break;
      }
    }
  }
  throw lastError;
}

export {
  cdp,
  CDP,
  options,
  getTargets,
  createTarget,
  activateTarget,
  closeTarget,
  CONNECTION_REFUSED_ERROR_CODE,
  CONNECTION_ERROR_CODE,
};
