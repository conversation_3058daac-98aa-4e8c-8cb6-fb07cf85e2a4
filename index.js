import puppeteer from "puppeteer";
import http from "http";
import { WebSocketServer } from "ws";
import { CDP } from "./simple-cdp";

// ---------------------- HTTP SERVER ---------------------- //
const server = http.createServer((req, res) => {
  if (req.url === "/") {
    res.writeHead(200, { "Content-Type": "text/html" });
    res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>Screencast Viewer</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1280px;
            margin: 0 auto;
            text-align: center;
        }
        canvas {
            width: 100%;
            max-width: 1280px;
            border: 2px solid #333;
            border-radius: 8px;
            background: #000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Live Screencast</h1>
        <div id="status" class="status disconnected">Connecting...</div>
        <canvas id="screen" width="1280" height="720" tabindex="0" style="outline: none; cursor: crosshair;"></canvas>
    </div>

   <script>
  const canvas = document.getElementById("screen");
  const ctx = canvas.getContext("2d");
  const status = document.getElementById("status");

  // Track how the current frame is drawn so we can map pointer coords -> normalized (0..1)
  const drawState = { offsetX: 0, offsetY: 0, drawWidth: 0, drawHeight: 0, naturalWidth: 0, naturalHeight: 0 };

  const ws = new WebSocket("ws://localhost:3001");
  ws.binaryType = "arraybuffer";

  ws.onopen = () => {
    status.textContent = "✅ Connected";
    status.className = "status connected";
  };

  ws.onclose = () => {
    status.textContent = "❌ Disconnected";
    status.className = "status disconnected";
  };

  ws.onmessage = (event) => {
    // We send binary frames (ArrayBuffer) and may send JSON control messages (string) later
    if (typeof event.data === 'string') {
      // Optional: handle JSON control messages from server
      return;
    }

    const blob = new Blob([event.data], { type: "image/jpeg" });
    const img = new Image();

    img.onload = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Compute aspect ratio scaling
      const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
      const drawWidth = img.width * scale;
      const drawHeight = img.height * scale;

      // Center the image
      const offsetX = (canvas.width - drawWidth) / 2;
      const offsetY = (canvas.height - drawHeight) / 2;

      // Save draw state for coordinate mapping
      drawState.offsetX = offsetX;
      drawState.offsetY = offsetY;
      drawState.drawWidth = drawWidth;
      drawState.drawHeight = drawHeight;
      drawState.naturalWidth = img.width;
      drawState.naturalHeight = img.height;

      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
      URL.revokeObjectURL(img.src);
    };

    img.src = URL.createObjectURL(blob);
  };

  // ----- Input capture -----
  function toNormalized(ev) {
    const rect = canvas.getBoundingClientRect();
    const cx = ev.clientX - rect.left;
    const cy = ev.clientY - rect.top;
    const { offsetX, offsetY, drawWidth, drawHeight } = drawState;
    // Only if inside the drawn video rectangle
    if (cx < offsetX || cy < offsetY || cx > offsetX + drawWidth || cy > offsetY + drawHeight) {
      return null;
    }
    const u = (cx - offsetX) / drawWidth; // 0..1 across video image
    const v = (cy - offsetY) / drawHeight; // 0..1 down video image
    return { u, v };
  }

  function send(msg) {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(msg));
    }
  }

  // Focus on click so we receive keyboard events
  canvas.addEventListener('mousedown', (ev) => {
    canvas.focus();
    const norm = toNormalized(ev);
    if (!norm) return;
    send({ type: 'mouse', action: 'down', button: ev.button === 2 ? 'right' : ev.button === 1 ? 'middle' : 'left', ...norm });
  });

  canvas.addEventListener('mouseup', (ev) => {
    const norm = toNormalized(ev);
    if (!norm) return;
    send({ type: 'mouse', action: 'up', button: ev.button === 2 ? 'right' : ev.button === 1 ? 'middle' : 'left', ...norm });
  });

  canvas.addEventListener('mousemove', (ev) => {
    const norm = toNormalized(ev);
    if (!norm) return;
    send({ type: 'mouse', action: 'move', ...norm });
  });

  canvas.addEventListener('wheel', (ev) => {
    const norm = toNormalized(ev);
    if (!norm) return;
    send({ type: 'mouse', action: 'wheel', deltaX: ev.deltaX, deltaY: ev.deltaY, ...norm });
    ev.preventDefault();
  }, { passive: false });

  canvas.addEventListener('contextmenu', (ev) => {
    ev.preventDefault();
  });

  function modifierMask(ev) {
    return (ev.altKey ? 1 : 0) | (ev.ctrlKey ? 2 : 0) | (ev.metaKey ? 4 : 0) | (ev.shiftKey ? 8 : 0);
  }

  canvas.addEventListener('keydown', (ev) => {
    const msg = { type: 'key', action: 'down', key: ev.key, code: ev.code, keyCode: ev.keyCode, modifiers: modifierMask(ev) };
    send(msg);
    // Send a 'char' for printable keys without ctrl/meta
    if (ev.key && ev.key.length === 1 && !ev.ctrlKey && !ev.metaKey) {
      send({ type: 'key', action: 'char', text: ev.key, modifiers: modifierMask(ev) });
    }
    // Prevent browser shortcuts from interfering
    if (ev.ctrlKey || ev.metaKey) ev.preventDefault();
  });

  canvas.addEventListener('keyup', (ev) => {
    const msg = { type: 'key', action: 'up', key: ev.key, code: ev.code, keyCode: ev.keyCode, modifiers: modifierMask(ev) };
    send(msg);
  });
</script>

</body>
</html>
    `);
  } else {
    res.writeHead(404);
    res.end("Not found");
  }
});

// ---------------------- WEBSOCKET SERVER ---------------------- //
const wss = new WebSocketServer({ port: 3001 });
let wsClients = new Set();

wss.on("connection", (ws) => {
  console.log("📱 Client connected");
  wsClients.add(ws);

  ws.on("message", async (data) => {
    try {
      const text = typeof data === "string" ? data : data.toString();
      const msg = JSON.parse(text);
      if (msg.type === "mouse") {
        await handleMouse(msg);
      } else if (msg.type === "key") {
        await handleKey(msg);
      }
    } catch (e) {
      console.warn("⚠️ Failed to handle client message:", e?.message || e);
    }
  });

  ws.on("close", () => {
    console.log("📱 Client disconnected");
    wsClients.delete(ws);
  });
});

// ---- Shared state for input replay ----
let activePage = null;
let activeClient = null;
let lastFrameMeta = null; // from Page.screencastFrame.metadata
let mouseButtonsMask = 0; // bitfield: 1=left, 2=right, 4=middle

function mapButtonToBit(btn) {
  switch (btn) {
    case "left":
      return 1;
    case "right":
      return 2;
    case "middle":
      return 4;
    default:
      return 0;
  }
}

function mapButton(btn) {
  if (btn === "right" || btn === "middle" || btn === "left") return btn;
  return "left";
}

function normToXY(u, v) {
  if (!lastFrameMeta) return null;
  const w = lastFrameMeta.deviceWidth || lastFrameMeta.width || 1280;
  const h = lastFrameMeta.deviceHeight || lastFrameMeta.height || 720;
  return { x: Math.round(u * w), y: Math.round(v * h) };
}

async function handleMouse(msg) {
  if (!activeClient) return;
  const pos =
    typeof msg.u === "number" && typeof msg.v === "number"
      ? normToXY(msg.u, msg.v)
      : null;
  const base = {
    type: "mouseMoved",
    x: pos ? pos.x : 0,
    y: pos ? pos.y : 0,
    pointerType: "mouse",
  };
  try {
    if (msg.action === "move" && pos) {
      await activeClient.send("Input.dispatchMouseEvent", {
        ...base,
        type: "mouseMoved",
        buttons: mouseButtonsMask,
      });
    } else if (msg.action === "down" && pos) {
      const bit = mapButtonToBit(msg.button);
      mouseButtonsMask |= bit;
      await activeClient.send("Input.dispatchMouseEvent", {
        ...base,
        type: "mousePressed",
        button: mapButton(msg.button),
        clickCount: 1,
      });
    } else if (msg.action === "up" && pos) {
      const bit = mapButtonToBit(msg.button);
      mouseButtonsMask &= ~bit;
      await activeClient.send("Input.dispatchMouseEvent", {
        ...base,
        type: "mouseReleased",
        button: mapButton(msg.button),
        clickCount: 1,
      });
    } else if (msg.action === "wheel" && pos) {
      await activeClient.send("Input.dispatchMouseEvent", {
        ...base,
        type: "mouseWheel",
        deltaX: msg.deltaX || 0,
        deltaY: msg.deltaY || 0,
      });
    }
  } catch (e) {
    console.warn("Mouse replay failed:", e?.message || e);
  }
}

async function handleKey(msg) {
  if (!activeClient) return;
  const base = { modifiers: msg.modifiers || 0 };
  try {
    if (msg.action === "down") {
      await activeClient.send("Input.dispatchKeyEvent", {
        type: "keyDown",
        key: msg.key,
        code: msg.code,
        windowsVirtualKeyCode: msg.keyCode || 0,
        ...base,
      });
    } else if (msg.action === "up") {
      await activeClient.send("Input.dispatchKeyEvent", {
        type: "keyUp",
        key: msg.key,
        code: msg.code,
        windowsVirtualKeyCode: msg.keyCode || 0,
        ...base,
      });
    } else if (msg.action === "char") {
      await activeClient.send("Input.dispatchKeyEvent", {
        type: "char",
        text: msg.text || "",
        ...base,
      });
    }
  } catch (e) {
    console.warn("Key replay failed:", e?.message || e);
  }
}

// Broadcast binary JPEG
function broadcastFrame(frameData) {
  const buffer = Buffer.from(frameData, "base64"); // Puppeteer gives base64
  wsClients.forEach((ws) => {
    if (ws.readyState === ws.OPEN) {
      ws.send(buffer, { binary: true });
    }
  });
}

// ---------------------- PUPPETEER ---------------------- //
(async () => {
  try {
    console.log("🔍 Connecting to Chrome...");
    const response = await fetch("http://localhost:9222/json/version");
    const wsEndpoint = await response.json();
    console.log("🔗 Chrome endpoint:", wsEndpoint.webSocketDebuggerUrl);

    const browser = await puppeteer.connect({
      headless: false,
      defaultViewport: null,
      browserWSEndpoint: wsEndpoint.webSocketDebuggerUrl,
    });
    console.log("✅ Connected to browser");

    const parentStack = new Map();

    async function startScreencast(page) {
      if (activeClient) {
        try {
          await activeClient.send("Page.stopScreencast");
        } catch {}
      }

      const client = await page.target().createCDPSession();
      await client.send("Page.enable");
      await client.send("Page.bringToFront");

      client.on("Page.screencastFrame", async (frame) => {
        // capture metadata for input coordinate mapping
        if (frame.metadata) {
          lastFrameMeta = frame.metadata;
        }
        broadcastFrame(frame.data);
        await client.send("Page.screencastFrameAck", {
          sessionId: frame.sessionId,
        });
      });

      await client.send("Page.startScreencast", {
        format: "jpeg",
        quality: 70,
        maxWidth: 1280,
        maxHeight: 720,
        everyNthFrame: 1,
      });

      activePage = page;
      activeClient = client;
      console.log("✅ Screencast started on:", page.url());
    }

    function watchPage(page) {
      page.on("popup", async (popup) => {
        // take a screenshot of the popup page
        const screenshot = await popup.screenshot();
        console.log({ screenshot });
        console.log("🆕 Popup detected:", popup.url(), { popup });
        parentStack.set(popup, page);
        await startScreencast(popup);
        watchPage(popup);
      });

      page.on("framenavigated", (frame) => {
        if (frame === page.mainFrame()) {
          console.log("🔗 Navigated:", frame.url());
        }
      });
    }

    browser.on("targetdestroyed", async (target) => {
      const targetPage = await target.page().catch(() => null);
      if (!targetPage) return;

      console.log("❌ Target closed:", targetPage.url());

      if (activePage === targetPage) {
        const parent = parentStack.get(targetPage);
        parentStack.delete(targetPage);
        if (parent && !parent.isClosed()) {
          console.log("🔄 Switching back to parent tab:", parent.url());
          await startScreencast(parent);
        }
      }
    });

    const page = await browser.newPage();
    watchPage(page);

    await page.goto("https://uber.com", { waitUntil: "networkidle2" });
    await startScreencast(page);

    console.log("🌐 Opened Uber.com and streaming screencast...");

    server.listen(3000, () => {
      console.log("🌐 Web client available at: http://localhost:3000");
      console.log("📡 WebSocket server running on port 3001");
    });
  } catch (error) {
    console.error("❌ Error:", error.message);
    console.log(
      "💡 Make sure Chrome is running with: chrome --remote-debugging-port=9222"
    );
  }
})();
